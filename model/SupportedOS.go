package model

import "patch-central-repo/common"

type SupportedOS struct {
	BaseEntityModel
	OsName               string               `bun:"type:varchar(100)" json:"osName"`
	OsVersion            string               `bun:"type:varchar(100)" json:"osVersion"`
	OsPlatform           common.UnixOsFlavour `json:"osPlatform"`
	DirStructure         string               `bun:"type:varchar(500)" json:"dirStructure"`
	SupportedRepoForOs   []string             `json:"supportedRepoForOs"`
	SupportedDirs        []string             `json:"supportedDirs"`
	MirrorAlternativUrls []string             `json:"mirrorAlternativUrls"`
	SupportedArch        []string             `json:"supportedArch"`
}
