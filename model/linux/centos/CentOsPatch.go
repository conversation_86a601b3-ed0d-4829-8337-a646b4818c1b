package centos

import "patch-central-repo/model"

type CentOsPatch struct {
	model.BaseEntityModel
	PkgId             string `bun:"type:varchar(250)" json:"pkgId"`
	PkgName           string `bun:"type:varchar(500)" json:"pkgName"`
	Arch              string `bun:"type:varchar(50)" json:"arch"`
	Version           string `bun:"type:varchar(100)" json:"version"`
	ReleaseVersion    string `bun:"type:varchar(100)" json:"releaseVersion"`
	Description       string `bun:"type:text" json:"description"`
	Distribution      string `bun:"type:varchar(100)" json:"distribution"`
	ReleaseDate       int64  `json:"releaseDate"`
	Title             string `bun:"type:varchar(500)" json:"title"`
	BinaryPkgName     string `bun:"type:varchar(500)" json:"binaryPkgName"`
	SrcPkgName        string `bun:"type:varchar(500)" json:"srcPkgName"`
	DownloadUrl       string `bun:"type:varchar(1000)" json:"downloadUrl"`
	PkgSize           int64  `json:"pkgSize"`
	Dependencies      string `bun:"type:text" json:"dependencies"`
	DependenciesPkgId string `bun:"type:text" json:"dependenciesPkgId"`
	Severity          string `bun:"type:varchar(50)" json:"severity"`
	BulletinId        string `bun:"type:varchar(100)" json:"bulletinId"`
	SupportUrl        string `bun:"type:varchar(500)" json:"supportUrl"`
}
