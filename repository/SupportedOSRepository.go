package repository

import (
	"github.com/uptrace/bun"
	"golang.org/x/net/context"
	"patch-central-repo/common"
	"patch-central-repo/db"
	"patch-central-repo/logger"
	"patch-central-repo/model"
)

type SupportedOSRepository struct {
	dbConnection *bun.DB
}

func NewSupportedOSRepository() *SupportedOSRepository {
	return &SupportedOSRepository{
		dbConnection: db.Connection,
	}
}

func (repo SupportedOSRepository) Save(supportedOS *model.SupportedOS) (*model.SupportedOS, error) {
	if supportedOS.Id == 0 {
		// Create new record
		_, err := repo.dbConnection.NewInsert().Model(supportedOS).Returning("id").Exec(context.Background())
		if err != nil {
			logger.ServiceLogger.Error("[SupportedOSRepository Save]", err.Error())
			return nil, err
		}
	} else {
		// Update existing record
		_, err := repo.dbConnection.NewUpdate().Model(supportedOS).WherePK().Exec(context.Background())
		if err != nil {
			logger.ServiceLogger.Error("[SupportedOSRepository Save]", err.Error())
			return nil, err
		}
	}
	return supportedOS, nil
}

func (repo SupportedOSRepository) FindByOsPlatform(osPlatform common.UnixOsFlavour) (*model.SupportedOS, error) {
	var supportedOS model.SupportedOS
	err := repo.dbConnection.NewSelect().Model(&supportedOS).Where("os_platform = ?", osPlatform).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[SupportedOSRepository FindByOsPlatform]", err.Error())
		return nil, err
	}
	return &supportedOS, nil
}

func (repo SupportedOSRepository) Delete(supportedOS *model.SupportedOS) (int64, error) {
	_, err := repo.dbConnection.NewDelete().Model(supportedOS).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[SupportedOSRepository Delete]", err.Error())
		return 0, err
	}
	return supportedOS.Id, nil
}
