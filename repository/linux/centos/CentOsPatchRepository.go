package centos

import (
	"github.com/uptrace/bun"
	"golang.org/x/net/context"
	"patch-central-repo/db"
	"patch-central-repo/logger"
	"patch-central-repo/model/linux/centos"
)

type CentOsPatchRepository struct {
	dbConnection *bun.DB
}

func NewCentOsPatchRepository() *CentOsPatchRepository {
	return &CentOsPatchRepository{
		dbConnection: db.Connection,
	}
}

func (repo CentOsPatchRepository) Save(centOsPatch *centos.CentOsPatch) (*centos.CentOsPatch, error) {
	if centOsPatch.Id == 0 {
		// Create new record
		_, err := repo.dbConnection.NewInsert().Model(centOsPatch).Returning("id").Exec(context.Background())
		if err != nil {
			logger.ServiceLogger.Error("[CentOsPatchRepository Save]", err.Error())
			return nil, err
		}
	} else {
		// Update existing record
		_, err := repo.dbConnection.NewUpdate().Model(centOsPatch).WherePK().Exec(context.Background())
		if err != nil {
			logger.ServiceLogger.Error("[CentOsPatchRepository Save]", err.Error())
			return nil, err
		}
	}
	return centOsPatch, nil
}

func (repo CentOsPatchRepository) FindByPkgId(pkgId string) (*centos.CentOsPatch, error) {
	var centOsPatch centos.CentOsPatch
	err := repo.dbConnection.NewSelect().Model(&centOsPatch).Where("pkg_id = ?", pkgId).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[CentOsPatchRepository FindByPkgId]", err.Error())
		return nil, err
	}
	return &centOsPatch, nil
}

func (repo CentOsPatchRepository) GetUnresolvedDepPkgIdList() ([]string, error) {
	var pkgIds []string
	err := repo.dbConnection.NewSelect().
		Model((*centos.CentOsPatch)(nil)).
		Column("pkg_id").
		Where("dependencies IS NOT NULL AND dependencies != ''").
		Where("dependencies_pkg_id IS NULL OR dependencies_pkg_id = ''").
		Scan(context.Background(), &pkgIds)

	if err != nil {
		logger.ServiceLogger.Error("[CentOsPatchRepository GetUnresolvedDepPkgIdList]", err.Error())
		return nil, err
	}
	return pkgIds, nil
}

func (repo CentOsPatchRepository) GetListByPkgId(pkgIds []string) ([]centos.CentOsPatch, error) {
	var patches []centos.CentOsPatch
	err := repo.dbConnection.NewSelect().
		Model(&patches).
		Where("pkg_id IN (?)", bun.In(pkgIds)).
		Scan(context.Background())

	if err != nil {
		logger.ServiceLogger.Error("[CentOsPatchRepository GetListByPkgId]", err.Error())
		return nil, err
	}
	return patches, nil
}

func (repo CentOsPatchRepository) FindByNameAndArchAndDistribution(name, arch, distribution string) ([]centos.CentOsPatch, error) {
	var patches []centos.CentOsPatch
	err := repo.dbConnection.NewSelect().
		Model(&patches).
		Where("pkg_name = ? AND arch = ? AND distribution = ?", name, arch, distribution).
		Scan(context.Background())

	if err != nil {
		logger.ServiceLogger.Error("[CentOsPatchRepository FindByNameAndArchAndDistribution]", err.Error())
		return nil, err
	}
	return patches, nil
}

func (repo CentOsPatchRepository) Delete(centOsPatch *centos.CentOsPatch) (int64, error) {
	_, err := repo.dbConnection.NewDelete().Model(centOsPatch).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[CentOsPatchRepository Delete]", err.Error())
		return 0, err
	}
	return centOsPatch.Id, nil
}
