package centos

import (
	"github.com/uptrace/bun"
	"golang.org/x/net/context"
	"patch-central-repo/db"
	"patch-central-repo/logger"
	"patch-central-repo/model/linux/centos"
)

type CentOsScanHistoryRepository struct {
	dbConnection *bun.DB
}

func NewCentOsScanHistoryRepository() *CentOsScanHistoryRepository {
	return &CentOsScanHistoryRepository{
		dbConnection: db.Connection,
	}
}

func (repo CentOsScanHistoryRepository) Save(centOsScanHistory *centos.CentOsScanHistory) (*centos.CentOsScanHistory, error) {
	if centOsScanHistory.Id == 0 {
		// Create new record
		_, err := repo.dbConnection.NewInsert().Model(centOsScanHistory).Returning("id").Exec(context.Background())
		if err != nil {
			logger.ServiceLogger.Error("[CentOsScanHistoryRepository Save]", err.Error())
			return nil, err
		}
	} else {
		// Update existing record
		_, err := repo.dbConnection.NewUpdate().Model(centOsScanHistory).WherePK().Exec(context.Background())
		if err != nil {
			logger.ServiceLogger.Error("[CentOsScanHistoryRepository Save]", err.Error())
			return nil, err
		}
	}
	return centOsScanHistory, nil
}

func (repo CentOsScanHistoryRepository) FindByXmlName(xmlName string) (*centos.CentOsScanHistory, error) {
	var centOsScanHistory centos.CentOsScanHistory
	err := repo.dbConnection.NewSelect().Model(&centOsScanHistory).Where("xml_name = ?", xmlName).Scan(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[CentOsScanHistoryRepository FindByXmlName]", err.Error())
		return nil, err
	}
	return &centOsScanHistory, nil
}

func (repo CentOsScanHistoryRepository) Delete(centOsScanHistory *centos.CentOsScanHistory) (int64, error) {
	_, err := repo.dbConnection.NewDelete().Model(centOsScanHistory).WherePK().Exec(context.Background())
	if err != nil {
		logger.ServiceLogger.Error("[CentOsScanHistoryRepository Delete]", err.Error())
		return 0, err
	}
	return centOsScanHistory.Id, nil
}
