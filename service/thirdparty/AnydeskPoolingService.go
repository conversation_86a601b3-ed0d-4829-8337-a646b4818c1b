package thirdparty

import (
	"crypto/tls"
	"fmt"
	"io"
	"net/http"
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"patch-central-repo/model"
	"patch-central-repo/model/thirdparty"
	"regexp"
	"strings"
	"time"
)

var anydeskMetaData = map[string]interface{}{
	"uuid": "h1i2j3k4-f5g6-7890-efgh-123456789hij",
	"templateFileNameMap": map[string]interface{}{
		"x64": "ANYDESK_X64.xml",
	},
}

type AnydeskPoolingService struct {
	ThirdPartyPackageService
}

func (c AnydeskPoolingService) Name() string {
	return "AnydeskPoolingService"
}

func init() {
	RegisterCollector(AnydeskPoolingService{})
}

func NewAnydeskPoolingService() *AnydeskPoolingService {
	return &AnydeskPoolingService{}
}

func (service AnydeskPoolingService) ExecuteSync() {
	logger.ServiceLogger.Debug("Fetching AnyDesk data for Windows platform")

	// Fetch latest patch data
	err := service.fetchLatestPatchData()
	if err != nil {
		logger.ServiceLogger.Error("Error while fetching AnyDesk data: ", err)
		return
	}

	// Create allpatchlist.txt and 7z files
	service.CreateRequiredFiles(common.ANYDESK)
}

func (service AnydeskPoolingService) fetchLatestPatchData() error {
	// AnyDesk uses a fixed URL that always points to the latest version
	downloadURL := "https://download.anydesk.com/AnyDesk.exe"

	// Get version from changelog page
	version, err := service.getLatestVersionFromChangelog()
	if err != nil {
		return fmt.Errorf("error getting latest version: %w", err)
	}

	logger.ServiceLogger.Debug("Found AnyDesk version: ", version)

	// Create release package if required
	err = service.createReleasePackageIfRequired(version, downloadURL, common.X64)
	if err != nil {
		return fmt.Errorf("error creating release package: %w", err)
	}

	return nil
}

func (service AnydeskPoolingService) getLatestVersionFromChangelog() (string, error) {
	url := "https://anydesk.com/en/changelog/windows"

	// Create HTTP client with TLS config
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: transport}

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return "", fmt.Errorf("error creating HTTP request: %w", err)
	}

	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("error making HTTP request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("HTTP request failed with status: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("error reading response body: %w", err)
	}

	// Parse the changelog to find the latest version
	version, err := service.parseAnydeskVersion(string(body))
	if err != nil {
		return "", fmt.Errorf("error parsing AnyDesk version: %w", err)
	}

	return version, nil
}

func (service AnydeskPoolingService) parseAnydeskVersion(htmlContent string) (string, error) {
	// Look for the first version in the changelog
	// Pattern: Version X.X.X
	re := regexp.MustCompile(`Version\s+(\d+\.\d+\.\d+)`)
	matches := re.FindStringSubmatch(htmlContent)

	if len(matches) < 2 {
		return "", fmt.Errorf("could not find AnyDesk version in changelog")
	}

	version := matches[1]
	return version, nil
}

func (service AnydeskPoolingService) createReleasePackageIfRequired(version, downloadURL string, osArch common.OsArchitecture) error {
	thirdPartyRepo := NewThirdPartyPackageService().Repository

	// Check if package already exists for this version and architecture
	existingPkg, _ := thirdPartyRepo.GetPkgByPlatformOsArchApplication(int(osArch), int(common.Windows), int(common.ANYDESK))
	if existingPkg.Id > 0 && existingPkg.Version == version {
		logger.ServiceLogger.Debug("Data already exists for AnyDesk version ", version, " arch ", osArch.String())
		return nil
	}

	logger.ServiceLogger.Debug("New Version found for AnyDesk ", version, " creating package data")

	// Get file size and headers
	headers := common.GetHeadersFromUrl(downloadURL)
	if headers == nil {
		return fmt.Errorf("could not get headers for download URL: %s", downloadURL)
	}

	fileSize := common.GetFileSizeFromUrl(downloadURL)
	fileName := fmt.Sprintf("AnyDesk-%s.exe", version)

	// Use current time as release date since AnyDesk doesn't provide this info in the direct download
	releaseTime := time.Now()

	// Delete existing package if it exists
	if existingPkg.Id > 0 {
		DeleteXmlForWindows(existingPkg.Uuid, common.ANYDESK)
		_, _ = thirdPartyRepo.DeletePatch(existingPkg)
	}

	// Create new package
	uuid := strings.ToLower(fmt.Sprintf("%s_%s_%s", common.ANYDESK.String(), version, osArch.String()))

	pkg := thirdparty.ThirdPartyPackage{
		BaseEntityModel: model.BaseEntityModel{
			Name: "AnyDesk",
		},
		Description:      "AnyDesk is a remote desktop application that provides platform independent remote access to PCs and other devices. It offers unparalleled performance with 60 fps and low latency.",
		Version:          version,
		Os:               common.Windows,
		Arch:             osArch,
		LanguageCode:     "en-US",
		LatestPackageUrl: downloadURL,
		Publisher:        "AnyDesk Software GmbH",
		SupportUrl:       "https://support.anydesk.com/knowledge",
		ReleaseNote:      "https://anydesk.com/en/changelog/windows",
		ReleaseDate:      releaseTime.UnixMilli(),
		Application:      common.ANYDESK,
		Uuid:             uuid,
	}

	// Create file data
	fileData := model.FileData{
		FileName:    fileName,
		DownloadUrl: downloadURL,
		Size:        fileSize,
		ReleaseDate: releaseTime.UnixMilli(),
	}
	pkg.PkgFileData = []model.FileData{fileData}

	// Generate install and uninstall commands
	installCmd, uninstallCmd := GenerateInstallUninstallCommands(common.ANYDESK)
	pkg.InstallCommand = installCmd
	pkg.UnInstallCommand = uninstallCmd

	pkg.CreatedTime = time.Now().UnixMilli()
	pkg.UpdatedTime = time.Now().UnixMilli()

	_, err := thirdPartyRepo.Create(&pkg)
	if err != nil {
		return fmt.Errorf("error creating AnyDesk package: %w", err)
	}

	// Generate XML file for Windows
	GenerateXmlForWindows(anydeskMetaData, version, osArch, uuid, common.ANYDESK)

	logger.ServiceLogger.Debug("Package data created successfully for AnyDesk version ", version)
	return nil
}
