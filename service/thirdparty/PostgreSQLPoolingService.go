package thirdparty

import (
	"crypto/tls"
	"fmt"
	"io"
	"net/http"
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"patch-central-repo/model"
	"patch-central-repo/model/thirdparty"
	"regexp"
	"strings"
	"time"
)

var postgresqlMetaData = map[string]interface{}{
	"uuid": "k1l2m3n4-h5i6-7890-ghij-123456789klm",
	"templateFileNameMap": map[string]interface{}{
		"x64": "POSTGRESQL_X64.xml",
	},
}

type PostgreSQLPoolingService struct {
	ThirdPartyPackageService
}

func (c PostgreSQLPoolingService) Name() string {
	return "PostgreSQLPoolingService"
}

func init() {
	RegisterCollector(PostgreSQLPoolingService{})
}

func NewPostgreSQLPoolingService() *PostgreSQLPoolingService {
	return &PostgreSQLPoolingService{}
}

func (service PostgreSQLPoolingService) ExecuteSync() {
	logger.ServiceLogger.Debug("Fetching PostgreSQL data for Windows platform")

	// Fetch latest patch data
	err := service.fetchLatestPatchData()
	if err != nil {
		logger.ServiceLogger.Error("Error while fetching PostgreSQL data: ", err)
		return
	}

	// Create allpatchlist.txt and 7z files
	service.CreateRequiredFiles(common.POSTGRESQL)
}

func (service PostgreSQLPoolingService) fetchLatestPatchData() error {
	// Create HTTP client with TLS configuration
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}

	// Fetch the PostgreSQL downloads page
	resp, err := client.Get("https://www.enterprisedb.com/downloads/postgres-postgresql-downloads")
	if err != nil {
		return fmt.Errorf("error fetching PostgreSQL downloads page: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP request failed with status: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("error reading response body: %w", err)
	}

	// Parse the page to find the latest version and download URL
	version, downloadURL, err := service.parsePostgreSQLVersion(string(body))
	if err != nil {
		return fmt.Errorf("error parsing PostgreSQL version: %w", err)
	}

	logger.ServiceLogger.Debug("Found PostgreSQL version: ", version)

	// Create release package if required
	err = service.createReleasePackageIfRequired(version, downloadURL, common.X64)
	if err != nil {
		return fmt.Errorf("error creating release package: %w", err)
	}

	return nil
}

func (service PostgreSQLPoolingService) parsePostgreSQLVersion(htmlContent string) (string, string, error) {
	// Look for the page-wrapper div and properly parse the nested structure
	// The table has columns: PostgreSQL Version, Linux x86-64, Linux x86-32, Mac OS X, Windows x86-64, Windows x86-32

	// Find the page-wrapper div
	pageWrapperStart := strings.Index(htmlContent, `<div id="page-wrapper">`)
	if pageWrapperStart == -1 {
		return "", "", fmt.Errorf("page-wrapper div not found")
	}

	// Find the matching closing div for page-wrapper by counting div tags
	content := htmlContent[pageWrapperStart:]
	divCount := 0
	pageWrapperEnd := -1

	for i := 0; i < len(content); i++ {
		if strings.HasPrefix(content[i:], "<div") {
			divCount++
		} else if strings.HasPrefix(content[i:], "</div>") {
			divCount--
			if divCount == 0 {
				pageWrapperEnd = i + 6 // Include </div>
				break
			}
		}
	}

	if pageWrapperEnd == -1 {
		return "", "", fmt.Errorf("page-wrapper div end not found")
	}

	pageContent := content[:pageWrapperEnd]

	// Look for table structure - find table rows with version data
	// Pattern to match table rows containing version numbers
	tableRowPattern := regexp.MustCompile(`<tr[^>]*>(.*?)</tr>`)
	versionPattern := regexp.MustCompile(`(\d+\.\d+(?:\.\d+)?)`)
	downloadPattern := regexp.MustCompile(`href="(https://sbp\.enterprisedb\.com/getfile\.jsp\?fileid=\d+)"`)

	// Find all table rows
	tableRows := tableRowPattern.FindAllStringSubmatch(pageContent, -1)

	var latestVersion string
	var downloadURL string

	for _, row := range tableRows {
		rowContent := row[1] // Content inside <tr>...</tr>

		// Check if this row contains a version number
		if versionMatches := versionPattern.FindStringSubmatch(rowContent); len(versionMatches) > 0 {
			if latestVersion == "" { // Take the first (latest) version found
				latestVersion = versionMatches[1]

				// Find all download links in this row
				downloadMatches := downloadPattern.FindAllStringSubmatch(rowContent, -1)

				// The table structure is: Version | Linux x86-64 | Linux x86-32 | Mac OS X | Windows x86-64 | Windows x86-32
				// We want the Windows x86-64 column (second occurrence of the download pattern)
				if len(downloadMatches) >= 2 {
					downloadURL = downloadMatches[1][1] // Second download link (index 1)
				} else if len(downloadMatches) >= 1 {
					// Fallback: take the first download link if we don't have enough
					downloadURL = downloadMatches[0][1]
				}

				if downloadURL != "" {
					break
				}
			}
		}
	}

	// If we didn't find the version in table rows, try a broader search within page-wrapper
	if latestVersion == "" {
		versionMatches := versionPattern.FindAllStringSubmatch(pageContent, -1)
		downloadMatches := downloadPattern.FindAllStringSubmatch(pageContent, -1)

		if len(versionMatches) > 0 {
			latestVersion = versionMatches[0][1] // Take first version found
		}

		if len(downloadMatches) > 0 {
			// Get the Windows x64 download (second occurrence of the pattern)
			if len(downloadMatches) >= 2 {
				downloadURL = downloadMatches[1][1] // Second download link (index 1)
			} else {
				downloadURL = downloadMatches[0][1] // First download link as fallback
			}
		}
	}

	if latestVersion == "" {
		return "", "", fmt.Errorf("no version found in PostgreSQL downloads page")
	}

	if downloadURL == "" {
		return "", "", fmt.Errorf("no Windows x64 download URL found for PostgreSQL version %s", latestVersion)
	}

	return latestVersion, downloadURL, nil
}

func (service PostgreSQLPoolingService) createReleasePackageIfRequired(version, downloadURL string, osArch common.OsArchitecture) error {
	thirdPartyRepo := NewThirdPartyPackageService().Repository

	// Check if package already exists
	uuid := strings.ToLower(fmt.Sprintf("%s_%s_%s", common.POSTGRESQL.String(), version, osArch.String()))
	existingPkg := thirdPartyRepo.GetByUUId(uuid)

	if existingPkg.Id > 0 {
		logger.ServiceLogger.Debug("PostgreSQL package already exists for version: ", version)
		return nil
	}

	logger.ServiceLogger.Debug("New Version found for PostgreSQL ", version, " creating package data")

	// Get file size and headers
	headers := common.GetHeadersFromUrl(downloadURL)
	if headers == nil {
		return fmt.Errorf("could not get headers for download URL: %s", downloadURL)
	}

	fileSize := common.GetFileSizeFromUrl(downloadURL)
	fileName := fmt.Sprintf("postgresql-%s-windows-x64.exe", version)

	// Use current time as release date since PostgreSQL doesn't provide this info
	releaseTime := time.Now()

	// Delete existing package if it exists
	if existingPkg.Id > 0 {
		DeleteXmlForWindows(existingPkg.Uuid, common.POSTGRESQL)
		_, _ = thirdPartyRepo.DeletePatch(existingPkg)
	}

	// Create new package
	uuid = strings.ToLower(fmt.Sprintf("%s_%s_%s", common.POSTGRESQL.String(), version, osArch.String()))

	pkg := thirdparty.ThirdPartyPackage{
		BaseEntityModel: model.BaseEntityModel{
			Name: "PostgreSQL",
		},
		Description:      "PostgreSQL is the world's most advanced open source database and the fourth most popular database. It is an object-oriented database that is fully ACID compliant and highly extensible.",
		Version:          version,
		Os:               common.Windows,
		Arch:             osArch,
		LanguageCode:     "en-US",
		LatestPackageUrl: downloadURL,
		Publisher:        "EnterpriseDB Corporation",
		SupportUrl:       "https://www.enterprisedb.com/support",
		ReleaseNote:      "https://www.postgresql.org/docs/release/",
		ReleaseDate:      releaseTime.UnixMilli(),
		Application:      common.POSTGRESQL,
		Uuid:             uuid,
	}

	// Create file data
	fileData := model.FileData{
		FileName:    fileName,
		DownloadUrl: downloadURL,
		Size:        fileSize,
		ReleaseDate: releaseTime.UnixMilli(),
	}
	pkg.PkgFileData = []model.FileData{fileData}

	// Generate install and uninstall commands
	installCmd, uninstallCmd := GenerateInstallUninstallCommands(common.POSTGRESQL)
	pkg.InstallCommand = installCmd
	pkg.UnInstallCommand = uninstallCmd

	pkg.CreatedTime = time.Now().UnixMilli()
	pkg.UpdatedTime = time.Now().UnixMilli()

	_, err := thirdPartyRepo.Create(&pkg)
	if err != nil {
		return fmt.Errorf("error creating PostgreSQL package: %w", err)
	}

	GenerateXmlForWindows(postgresqlMetaData, version, osArch, uuid, common.POSTGRESQL)

	logger.ServiceLogger.Debug("PostgreSQL package created successfully for version: ", version)
	return nil
}
