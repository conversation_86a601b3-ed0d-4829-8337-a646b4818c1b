package thirdparty

import (
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"patch-central-repo/model"
	"patch-central-repo/model/thirdparty"
	"strings"
	"time"
)

var postmanMetaData = map[string]interface{}{
	"uuid": "e1f2g3h4-c5d6-7890-bcde-123456789efg",
	"templateFileNameMap": map[string]interface{}{
		"x64": "POSTMAN_X64.xml",
	},
}

type PostmanPoolingService struct {
	ThirdPartyPackageService
}

func (c PostmanPoolingService) Name() string {
	return "PostmanPoolingService"
}

func init() {
	RegisterCollector(PostmanPoolingService{})
}

func NewPostmanPoolingService() *PostmanPoolingService {
	return &PostmanPoolingService{}
}

func (service PostmanPoolingService) ExecuteSync() {
	logger.ServiceLogger.Debug("Fetching Postman data for Windows platform")

	// Fetch latest patch data
	err := service.fetchLatestPatchData()
	if err != nil {
		logger.ServiceLogger.Error("Error while fetching Postman data: ", err)
		return
	}

	// Create allpatchlist.txt and 7z files
	service.CreateRequiredFiles(common.POSTMAN)
}

type PostmanReleaseNote struct {
	Version   string `json:"version"`
	Content   string `json:"content"`
	CreatedAt string `json:"createdAt"`
}

type PostmanReleaseResponse struct {
	Notes []PostmanReleaseNote `json:"notes"`
}

func (service PostmanPoolingService) fetchLatestPatchData() error {
	url := "https://mkt.cdn.postman.com/www-next/release-notes/app-release-notes.json"

	// Create HTTP client with TLS config
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: transport}

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return fmt.Errorf("error creating HTTP request: %w", err)
	}

	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("error making HTTP request: %w", err)
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			logger.ServiceLogger.Error(err)
		}
	}(resp.Body)

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("HTTP request failed with status code: %d", resp.StatusCode)
	}

	// Parse JSON response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("error reading response body: %w", err)
	}

	var releaseResponse PostmanReleaseResponse
	err = json.Unmarshal(body, &releaseResponse)
	if err != nil {
		return fmt.Errorf("error parsing JSON response: %w", err)
	}

	if len(releaseResponse.Notes) == 0 {
		return fmt.Errorf("no release notes found in Postman API response")
	}

	// Get the latest version (first in the array)
	latestRelease := releaseResponse.Notes[0]

	logger.ServiceLogger.Debug("Found Postman version: ", latestRelease.Version)

	// Create release package if required
	err = service.createReleasePackageIfRequired(latestRelease, common.X64)
	if err != nil {
		return fmt.Errorf("error creating release package: %w", err)
	}

	return nil
}

func (service PostmanPoolingService) createReleasePackageIfRequired(releaseNote PostmanReleaseNote, osArch common.OsArchitecture) error {
	thirdPartyRepo := NewThirdPartyPackageService().Repository

	// Check if package already exists for this version and architecture
	existingPkg, _ := thirdPartyRepo.GetPkgByPlatformOsArchApplication(int(osArch), int(common.Windows), int(common.POSTMAN))
	if existingPkg.Id > 0 && existingPkg.Version == releaseNote.Version {
		logger.ServiceLogger.Debug("Data already exists for Postman version ", releaseNote.Version, " arch ", osArch.String())
		return nil
	}

	logger.ServiceLogger.Debug("New Version found for Postman ", releaseNote.Version, " creating package data")

	// Fixed download URL as specified in requirements
	downloadURL := "https://dl.pstmn.io/download/latest/win64"

	// Get file size and headers
	headers := common.GetHeadersFromUrl(downloadURL)
	if headers == nil {
		return fmt.Errorf("could not get headers for download URL: %s", downloadURL)
	}

	fileSize := common.GetFileSizeFromUrl(downloadURL)
	fileName := "Postman-win64-" + releaseNote.Version + "-Setup.exe"

	// Parse release date from createdAt
	releaseTime, err := time.Parse(time.RFC3339, releaseNote.CreatedAt)
	if err != nil {
		logger.ServiceLogger.Error("Error parsing release date, using current time: ", err)
		releaseTime = time.Now()
	}

	// Delete existing package if it exists
	if existingPkg.Id > 0 {
		DeleteXmlForWindows(existingPkg.Uuid, common.POSTMAN)
		_, _ = thirdPartyRepo.DeletePatch(existingPkg)
	}

	// Create new package
	uuid := strings.ToLower(fmt.Sprintf("%s_%s_%s", common.POSTMAN.String(), releaseNote.Version, osArch.String()))

	pkg := thirdparty.ThirdPartyPackage{
		BaseEntityModel: model.BaseEntityModel{
			Name: "Postman",
		},
		Description:      "Modern software is built on APIs. Postman helps you develop APIs faster. Save requests and organize your APIs into Collections. The API library for your team Onboard developers to your API in seconds",
		Version:          releaseNote.Version,
		Os:               common.Windows,
		Arch:             osArch,
		LanguageCode:     "en-US",
		LatestPackageUrl: downloadURL,
		Publisher:        "Postman",
		SupportUrl:       "https://www.postman.com/support/",
		ReleaseNote:      "https://www.postman.com/downloads/release-notes/",
		ReleaseDate:      releaseTime.UnixMilli(),
		Application:      common.POSTMAN,
		Uuid:             uuid,
	}

	// Create file data
	fileData := model.FileData{
		FileName:    fileName,
		DownloadUrl: downloadURL,
		Size:        fileSize,
		ReleaseDate: releaseTime.UnixMilli(),
	}
	pkg.PkgFileData = []model.FileData{fileData}

	// Generate install and uninstall commands
	installCmd, uninstallCmd := GenerateInstallUninstallCommands(common.POSTMAN)
	pkg.InstallCommand = installCmd
	pkg.UnInstallCommand = uninstallCmd

	pkg.CreatedTime = time.Now().UnixMilli()
	pkg.UpdatedTime = time.Now().UnixMilli()

	_, err = thirdPartyRepo.Create(&pkg)
	if err != nil {
		return fmt.Errorf("error creating Postman package: %w", err)
	}

	// Generate XML file for Windows
	GenerateXmlForWindows(postmanMetaData, releaseNote.Version, osArch, uuid, common.POSTMAN)

	logger.ServiceLogger.Debug("Package data created successfully for Postman version ", releaseNote.Version)
	return nil
}
