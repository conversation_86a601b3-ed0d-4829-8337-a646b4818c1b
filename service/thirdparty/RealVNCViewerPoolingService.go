package thirdparty

import (
	"crypto/tls"
	"fmt"
	"io"
	"net/http"
	"patch-central-repo/common"
	"patch-central-repo/logger"
	"patch-central-repo/model"
	"patch-central-repo/model/thirdparty"
	"regexp"
	"strings"
	"time"
)

var realvncViewerMetaData = map[string]interface{}{
	"uuid": "k1l2m3n4-i5j6-7890-hijk-123456789klm",
	"templateFileNameMap": map[string]interface{}{
		"x64": "REALVNC_VIEWER_X64.xml",
	},
}

type RealVNCViewerPoolingService struct {
	ThirdPartyPackageService
}

func (c RealVNCViewerPoolingService) Name() string {
	return "RealVNCViewerPoolingService"
}

func init() {
	RegisterCollector(RealVNCViewerPoolingService{})
}

func NewRealVNCViewerPoolingService() *RealVNCViewerPoolingService {
	return &RealVNCViewerPoolingService{}
}

func (service RealVNCViewerPoolingService) ExecuteSync() {
	logger.ServiceLogger.Debug("Fetching RealVNC Viewer data for Windows platform")

	// Fetch latest patch data
	err := service.fetchLatestPatchData()
	if err != nil {
		logger.ServiceLogger.Error("Error while fetching RealVNC Viewer data: ", err)
		return
	}

	// Create allpatchlist.txt and 7z files
	service.CreateRequiredFiles(common.REALVNC_VIEWER)
}

func (service RealVNCViewerPoolingService) fetchLatestPatchData() error {
	// Get version from download page
	version, downloadURL, err := service.getLatestVersionFromDownloadPage()
	if err != nil {
		return fmt.Errorf("error getting latest version: %w", err)
	}

	logger.ServiceLogger.Debug("Found RealVNC Viewer version: ", version)

	// Create release package if required
	err = service.createReleasePackageIfRequired(version, downloadURL, common.X64)
	if err != nil {
		return fmt.Errorf("error creating release package: %w", err)
	}

	return nil
}

func (service RealVNCViewerPoolingService) getLatestVersionFromDownloadPage() (string, string, error) {
	// Create HTTP client with TLS configuration
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
	}
	client := &http.Client{Transport: tr}

	// Fetch the download page
	resp, err := client.Get("https://www.realvnc.com/en/connect/download/viewer/windows/")
	if err != nil {
		return "", "", fmt.Errorf("error fetching download page: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", "", fmt.Errorf("HTTP request failed with status code: %d", resp.StatusCode)
	}

	// Read the response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", "", fmt.Errorf("error reading response body: %w", err)
	}

	// Extract download URL from the page
	downloadURL, err := service.extractDownloadURL(string(body))
	if err != nil {
		return "", "", fmt.Errorf("error extracting download URL: %w", err)
	}

	// Extract version from download URL
	version := service.extractVersionFromURL(downloadURL)
	if version == "" {
		return "", "", fmt.Errorf("could not extract version from download URL: %s", downloadURL)
	}

	return version, downloadURL, nil
}

func (service RealVNCViewerPoolingService) extractDownloadURL(htmlContent string) (string, error) {
	// Look for the download URL pattern in the HTML
	// Pattern: https://downloads.realvnc.com/download/file/viewer.files/VNC-Viewer-X.X.X-Windows.exe
	re := regexp.MustCompile(`https://downloads\.realvnc\.com/download/file/viewer\.files/VNC-Viewer-[\d\.]+-Windows\.exe`)
	matches := re.FindStringSubmatch(htmlContent)

	if len(matches) == 0 {
		return "", fmt.Errorf("download URL not found in page content")
	}

	return matches[0], nil
}

func (service RealVNCViewerPoolingService) extractVersionFromURL(downloadURL string) string {
	// Extract version from URL pattern: VNC-Viewer-X.X.X-Windows.exe
	re := regexp.MustCompile(`VNC-Viewer-([\d\.]+)-Windows\.exe`)
	matches := re.FindStringSubmatch(downloadURL)

	if len(matches) >= 2 {
		return matches[1]
	}

	return ""
}

func (service RealVNCViewerPoolingService) createReleasePackageIfRequired(version, downloadURL string, osArch common.OsArchitecture) error {
	thirdPartyRepo := NewThirdPartyPackageService().Repository

	// Check if package already exists for this version and architecture
	existingPkg, _ := thirdPartyRepo.GetPkgByPlatformOsArchApplication(int(osArch), int(common.Windows), int(common.REALVNC_VIEWER))
	if existingPkg.Id > 0 && existingPkg.Version == version {
		logger.ServiceLogger.Debug("Data already exists for RealVNC Viewer version ", version, " arch ", osArch.String())
		return nil
	}

	logger.ServiceLogger.Debug("New Version found for RealVNC Viewer ", version, " creating package data")

	// Get file size and headers
	headers := common.GetHeadersFromUrl(downloadURL)
	if headers == nil {
		return fmt.Errorf("could not get headers for download URL: %s", downloadURL)
	}

	fileSize := common.GetFileSizeFromUrl(downloadURL)
	fileName := fmt.Sprintf("VNC-Viewer-%s-Windows.exe", version)

	// Use current time as release date since RealVNC doesn't provide this info in the direct download
	releaseTime := time.Now()

	// Delete existing package if it exists
	if existingPkg.Id > 0 {
		DeleteXmlForWindows(existingPkg.Uuid, common.REALVNC_VIEWER)
		_, _ = thirdPartyRepo.DeletePatch(existingPkg)
	}

	// Create new package
	uuid := strings.ToLower(fmt.Sprintf("%s_%s_%s", common.REALVNC_VIEWER.String(), version, osArch.String()))

	// Get metadata for UUID generation
	if metaData, exists := realvncViewerMetaData["uuid"]; exists {
		uuid = strings.ToLower(metaData.(string))
	}

	pkg := thirdparty.ThirdPartyPackage{
		BaseEntityModel: model.BaseEntityModel{
			Name: "RealVNC Viewer",
		},
		Description:      "RealVNC Viewer is a secure, reliable remote access software that allows you to connect to and control computers from anywhere in the world. It provides high-quality screen sharing, file transfer, and remote support capabilities.",
		Version:          version,
		Os:               common.Windows,
		Arch:             osArch,
		LanguageCode:     "en-US",
		LatestPackageUrl: downloadURL,
		Publisher:        "RealVNC Limited",
		SupportUrl:       "https://help.realvnc.com/hc/en-us",
		ReleaseNote:      "https://help.realvnc.com/hc/en-us/articles/************-Release-Notes",
		ReleaseDate:      releaseTime.UnixMilli(),
		Application:      common.REALVNC_VIEWER,
		Uuid:             uuid,
	}

	// Create file data
	fileData := model.FileData{
		FileName:    fileName,
		DownloadUrl: downloadURL,
		Size:        fileSize,
		ReleaseDate: releaseTime.UnixMilli(),
	}
	pkg.PkgFileData = []model.FileData{fileData}

	// Generate install and uninstall commands
	installCmd, uninstallCmd := GenerateInstallUninstallCommands(common.REALVNC_VIEWER)
	pkg.InstallCommand = installCmd
	pkg.UnInstallCommand = uninstallCmd

	pkg.CreatedTime = time.Now().UnixMilli()
	pkg.UpdatedTime = time.Now().UnixMilli()

	_, err := thirdPartyRepo.Create(&pkg)
	if err != nil {
		return fmt.Errorf("error creating RealVNC Viewer package: %w", err)
	}

	// Create XML file for Windows deployment
	GenerateXmlForWindows(realvncViewerMetaData, version, osArch, uuid, common.REALVNC_VIEWER)

	logger.ServiceLogger.Debug("Successfully created RealVNC Viewer package for version ", version)
	return nil
}
